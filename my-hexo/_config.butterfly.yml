# --------------------------------------
# Hexo Butterfly主题配置文件
# 如有任何问题，请参考官方文档
# 中文文档: https://butterfly.js.org/
# 英文文档: https://butterfly.js.org/en/
# --------------------------------------

# --------------------------------------
# 导航栏设置
# --------------------------------------

nav:
  # 导航栏logo图片
  logo:
  # 是否显示网站标题
  display_title: true
  # 是否固定导航栏在顶部
  fixed: true

# 菜单设置
# 格式: 名称: 链接 || 图标
# fas fa-home/fas fa-archive/fas fa-tags等是FontAwesome图标
menu:
    首页: / || fas fa-home
    时间轴: /archives/ || fas fa-archive
    标签: /tags/ || fas fa-tags
    分类: /categories/ || fas fa-folder-open
    照片: /Gallery/ || fas fa-images
    说说: /talks/ || fas fa-comment
    友链: /link/ || fas fa-link
    关于: /about/ || fas fa-heart




# --------------------------------------
# 代码块设置
# --------------------------------------

code_blocks:
  # 代码块主题: darker / pale night / light / ocean / false
  theme: ocean
  # 是否显示Mac风格的代码块
  macStyle: true
  # 代码块高度限制 (单位: px)，设置false关闭限制
  height_limit: false
  # 代码是否自动换行
  word_wrap: false

  # 工具栏设置
  # 是否显示复制按钮
  copy: true
  # 是否显示代码语言
  language: true
  # 代码块收缩/展开按钮: true-默认收缩 / false-默认展开 / none-不显示按钮
  shrink: false
  # 是否支持全页展示
  fullpage: false

# 社交媒体链接
# 格式:
#   图标: 链接 || 描述 || 颜色
social:
   #fab fa-github: https://github.com/jiaeboy || Github || '#24292e'
   iconfont icon--bilibili: https://space.bilibili.com/516113483 || Bilibili || '#FB7299'
   iconfont icon-weixin: https://www.baidu.com || WeChat || '#07C160'
   iconfont icon-telegram: https://t.me/Dargon_88 || Telegram || '#0088CC'
   iconfont icon-linux: https://linux.do/ || Linux || '#FCC624'

# --------------------------------------
# 图片设置
# --------------------------------------

# 网站图标
favicon: /image/avatar.png

# 头像设置
avatar:
  # 头像图片链接
  img: /image/lhck.png
  # 头像旋转效果
  effect: false

# 禁用所有横幅图片
disable_top_img: true

# 如果页面的顶部图像未设置，它将显示默认的顶部图像
default_top_img:

# 首页的横幅图片
index_img:

# 归档页的横幅图片
archive_img:

# 标签页的横幅图片(注意：是标签页，不是标签列表页)
tag_img:

# 为每个标签设置不同的横幅图片
# 格式:
#  - 标签名称: 图片链接
tag_per_img:

# 分类页的横幅图片(注意：是分类页，不是分类列表页)
category_img:

# 为每个分类设置不同的横幅图片
# 格式:
#  - 分类名称: 图片链接
category_per_img:

# 页脚背景图片
footer_img: false

# 网站背景
# 可以设置颜色或图片URL
background:

# 文章封面设置
cover:
  # 是否显示文章封面
  index_enable: true
  aside_enable: true
  archives_enable: true
  # 当封面未设置时，显示默认封面
  default_cover:
  - https://img.loliapi.cn/i/pc/img6.webp
  - https://img.loliapi.cn/i/pc/img121.webp
  - https://img.loliapi.cn/i/pc/img156.webp

# 替换损坏的图片
error_img:
  # 友情链接页面中损坏的图片替代品
  flink: /img/friend_404.gif
  # 文章页中损坏的图片替代品
  post_page: /img/404.jpg

# 简单的404页面
error_404:
  enable: true
  subtitle: '页面未找到'
  background: /img/error-page.png

# 文章元数据显示设置
post_meta:
  # 首页
  page:
    # 日期类型: created(创建时间) / updated(更新时间) / both(都显示)
    date_type: created
    # 日期格式: date(日期) / relative(相对时间)
    date_format: date
    # 是否显示分类
    categories: true
    # 是否显示标签
    tags: true
    # 是否显示标签图标
    label: true
  # 文章页
  post:
    # 位置: left(左对齐) / center(居中)
    position: left
    # 日期类型: created(创建时间) / updated(更新时间) / both(都显示)
    date_type: both
    # 日期格式: date(日期) / relative(相对时间)
    date_format: date
    # 是否显示分类
    categories: true
    # 是否显示标签
    tags: true
    # 是否显示标签图标
    label: true

# --------------------------------------
# 首页设置
# --------------------------------------

# 首页顶部图片设置
# 默认：顶部图片全屏，站点信息居中
# 网站信息位置，例如：300px/300em/300rem/10%
index_site_info_top:
# 顶部图片高度，例如：300px/300em/300rem
index_top_img_height:

# 首页副标题
subtitle:
  enable: false
  # 打字机效果
  effect: true
  # 自定义打字机效果
  # https://github.com/mattboldt/typed.js/#customization
  typed_option:
  # 来源 - 调用第三方服务API（仅限中文）
  # 它将首先显示来源，然后显示内容
  # 选择: false/1/2/3
  # false - 禁用此功能
  # 1 - hitokoto.cn
  # 2 - https://api.aa1.cn/doc/yiyan.html
  # 3 - jinrishici.com
  source: false
  # 如果关闭打字机效果，副标题将只显示sub的第一行
  sub:

# 首页文章布局
# 1: 左图右文
# 2: 右图左文
# 3: 左右交替
# 4: 上图下文
# 5: 信息展示在封面上
# 6: 瀑布流布局 - 上图下文
# 7: 瀑布流布局 - 信息展示在封面上
index_layout: 3

# 首页文章简介显示
# 1: 只显示description字段
# 2: 优先显示description，没有则显示自动截取
# 3: 自动截取文章前N个字作为简介(默认)
# false: 不显示文章简介
index_post_content:
  method: 2
  # 当method为2或3时，截取的长度
  length: 500

# --------------------------------------
# 文章页设置
# --------------------------------------

# 目录(TOC)设置
toc:
  # 文章页是否显示目录
  post: true
  # 页面是否显示目录
  page: false
  # 是否显示目录序号
  number: true
  # 目录是否默认展开
  expand: false
  # 仅用于文章页
  # 是否使用简单样式
  style_simple: false
  # 是否显示滚动百分比
  scroll_percent: true

# 文章版权信息
post_copyright:
  enable: true
  # 是否自动解码URL
  decode: false
  # 版权链接
  author_href:
  # 版权协议
  license: CC BY-NC-SA 4.0
  # 版权协议链接
  license_url: https://creativecommons.org/licenses/by-nc-sa/4.0/

# 赞赏/打赏功能
reward:
  enable: false
  # 提示文本
  text:
  # 二维码配置
  QR_code:
    # - img: /img/wechat.jpg
    #   link:
    #   text: wechat
    # - img: /img/alipay.jpg
    #   link:
    #   text: alipay

# 文章编辑按钮
# 方便在线浏览和编辑博客源代码
post_edit:
  enable: false
  # 编辑URL
  # url: https://github.com/user-name/repo-name/edit/branch-name/subdirectory-name/
  # 例如: https://github.com/jerryc127/butterfly.js.org/edit/main/source/
  url:

# 相关文章推荐
related_post:
  enable: true
  # 显示的文章数量
  limit: 6
  # 按创建时间或更新时间排序: created / updated
  date_type: created

# 文章分页按钮
# 1: 较旧文章链接到旧文章
# 2: 较旧文章链接到新文章
# false: 禁用分页
post_pagination: 1

# 文章过期提醒
noticeOutdate:
  enable: false
  # 样式: simple / flat
  style: flat
  # 显示提醒的天数
  limit_day: 365
  # 位置: top / bottom
  position: top
  # 提示信息
  message_prev: 距离上次更新已经过去了
  message_next: 天，文章内容可能已经过时。

# --------------------------------------
# 页脚设置
# --------------------------------------
footer:
  # 网站所有者信息
  owner:
    enable: true
    # 网站创建年份
    since: 2025
  # 自定义页脚文本
  custom_text: <p><a style="margin-inline:5px" target="_blank" href="https://hexo.io/"><img src="https://img.shields.io/badge/Frame-Hexo-blue?style=flat&logo=hexo" title="博客框架为 Hexo" alt="HEXO"></a><a style="margin-inline:5px" target="_blank" href="https://butterfly.js.org/"><img src="https://img.shields.io/badge/Theme-Butterfly-6513df?style=flat&logo=bitdefender" title="主题采用 Butterfly" alt="Butterfly"></a><a style="margin-inline:5px" target="_blank" href="https://www.jsdelivr.com/"><img src="https://img.shields.io/badge/CDN-jsDelivr-orange?style=flat&logo=jsDelivr" title="本站使用 Jsdelivr 为静态资源提供CDN加速" alt="Jsdelivr"></a><a style="margin-inline:5px" target="_blank" href="https://github.com/"><img src="https://img.shields.io/badge/Source-Github-d021d6?style=flat&logo=GitHub" title="本站项目由 GitHub 托管" alt="GitHub"></a><a style="margin-inline:5px" target="_blank" href="http://creativecommons.org/licenses/by-nc-sa/4.0/"><img src="https://img.shields.io/badge/Copyright-BY--NC--SA%204.0-d42328?style=flat&logo=Claris" alt="img" title="本站采用知识共享署名-非商业性使用-相同方式共享4.0国际许可协议进行许可"></a></p>
  # 主题和框架版权信息
  copyright: false

# --------------------------------------
# 侧边栏设置
# --------------------------------------

aside:
  # 是否启用侧边栏
  enable: true
  # 是否默认隐藏
  hide: false
  # 是否在右下角显示隐藏侧边栏的按钮
  button: true
  # 是否在移动设备上显示
  mobile: true
  # 位置: left / right
  position: left
  # 侧边栏显示的卡片
  display:
    # 是否显示归档
    archive: true
    # 是否显示标签
    tag: true
    # 是否显示分类
    category: true
  # 作者卡片
  card_author:
    enable: true
    # 描述
    description:
    # 按钮
    button:
      enable: true
      icon: fab fa-github
      text: Follow Me Github
      link: https://github.com/jiaeboy
  # 公告卡片
  card_announcement:
    enable: true
    content: 人生就像品茗，懂得吃苦，才能回甘。
  # 最新文章卡片
  card_recent_post:
    enable: false
    # 显示数量，设为0显示所有
    limit: 5
    # 排序: date / updated
    sort: date
    sort_order:
  # 最新评论卡片
  card_newest_comments:
    enable: false
    sort_order:
    limit: 6
    # 存储时间 (单位: mins)，数据保存到localStorage
    storage: 10
    # 是否显示头像
    avatar: true
  # 分类卡片
  card_categories:
    enable: true
    # 显示数量，设为0显示所有
    limit: 8
    # 是否展开: none / true / false
    expand: none
    sort_order:
  # 标签卡片
  card_tags:
    enable: true
    # 显示数量，设为0显示所有
    limit: 40
    # 是否显示标签颜色
    color: true
    # 排序方式: random/name/length
    orderby: random
    # 排序顺序: 1升序/-1降序
    order: 1
    sort_order:
  # 归档卡片
  card_archives:
    enable: true
    # 类型: monthly / yearly
    type: monthly
    # 日期格式
    format: MMMM YYYY
    # 排序顺序: 1升序/-1降序
    order: -1
    # 显示数量，设为0显示所有
    limit: 8
    sort_order:
  # 文章系列卡片
  card_post_series:
    enable: true
    # 是否以系列名称作为标题
    series_title: false
    # 按标题或日期排序
    orderBy: 'date'
    # 排序顺序: 1升序/-1降序
    order: -1
  # 网站信息卡片
  card_webinfo:
    enable: true
    # 是否显示文章数量
    post_count: true
    # 是否显示最后更新日期
    last_push_date: true
    sort_order:
    # 网站运行时间，格式: 月/日/年 时间 或 年/月/日 时间
    # 留空则不启用
    runtime_date:

# --------------------------------------
# Bottom right button
# --------------------------------------

# The distance between the bottom right button and the bottom (default unit: px)
rightside_bottom:

# Conversion between Traditional and Simplified Chinese
translate:
  enable: false
  # The text of a button
  default: 繁
  # the language of website (1 - Traditional Chinese/ 2 - Simplified Chinese）
  defaultEncoding: 2
  # Time delay
  translateDelay: 0
  # The text of the button when the language is Simplified Chinese
  msgToTraditionalChinese: '繁'
  # The text of the button when the language is Traditional Chinese
  msgToSimplifiedChinese: '簡'

# Read Mode
readmode: true

# Dark Mode
darkmode:
  enable: true
  # Toggle Button to switch dark/light mode
  button: true
  # Switch dark/light mode automatically
  # autoChangeMode: 1  Following System Settings, if the system doesn't support dark mode, it will switch dark mode between 6 pm to 6 am
  # autoChangeMode: 2  Switch dark mode between 6 pm to 6 am
  # autoChangeMode: false
  autoChangeMode: false
  # Set the light mode time. The value is between 0 and 24. If not set, the default value is 6 and 18
  start:
  end:

# Show scroll percent in scroll-to-top button
rightside_scroll_percent: true

# Don't modify the following settings unless you know how they work
# Choose: readmode,translate,darkmode,hideAside,toc,chat,comment
# Don't repeat the same value
rightside_item_order:
  enable: false
  # Default: readmode,translate,darkmode,hideAside
  hide:
  # Default: toc,chat,comment
  show:

# --------------------------------------
# Global Settings
# --------------------------------------

anchor:
  # When you scroll, the URL will update according to header id.
  auto_update: false
  # Click the headline to scroll and update the anchor
  click_to_scroll: false

photofigcaption: false

copy:
  enable: true
  # Add the copyright information after copied content
  copyright:
    enable: false
    limit_count: 150

# Need to install the hexo-wordcount plugin
wordcount:
  enable: true
  # Display the word count of the article in post meta
  post_wordcount: true
  # Display the time to read the article in post meta
  min2read: true
  # Display the total word count of the website in aside's webinfo
  total_wordcount: true

# Busuanzi count for PV / UV in site
busuanzi:
  site_uv: true
  site_pv: true
  page_pv: true

# --------------------------------------
# Math
# --------------------------------------

# About the per_page
# if you set it to true, it will load mathjax/katex script in each page
# if you set it to false, it will load mathjax/katex script according to your setting (add the 'mathjax: true' or 'katex: true' in page's front-matter)
math:
  # Choose: mathjax, katex
  # Leave it empty if you don't need math
  use:
  per_page: true
  hide_scrollbar: false

  mathjax:
    # Enable the contextual menu
    enableMenu: true
    # Choose: all / ams / none, This controls whether equations are numbered and how
    tags: none

  katex:
    # Enable the copy KaTeX formula
    copy_tex: false

# --------------------------------------
# Search
# --------------------------------------

search:
  # Choose: algolia_search / local_search / docsearch
  # leave it empty if you don't need search
  use:
  placeholder:

  # Algolia Search
  algolia_search:
    # Number of search results per page
    hitsPerPage: 6

  # Local Search
  local_search:
    # Preload the search data when the page loads.
    preload: false
    # Show top n results per article, show all results by setting to -1
    top_n_per_article: 1
    # Unescape html strings to the readable one.
    unescape: false
    CDN:

  # Docsearch
  # https://docsearch.algolia.com/
  docsearch:
    appId:
    apiKey:
    indexName:
    option:

# --------------------------------------
# Share System
# --------------------------------------

share:
  # Choose: sharejs / addtoany
  # Leave it empty if you don't need share
  use: sharejs

  # Share.js
  # https://github.com/overtrue/share.js
  sharejs:
    sites: facebook,twitter,wechat,weibo,qq

  # AddToAny
  # https://www.addtoany.com/
  addtoany:
    item: facebook,twitter,wechat,sina_weibo,facebook_messenger,email,copy_link

# --------------------------------------
# Comments System
# --------------------------------------

comments:
  # Up to two comments system, the first will be shown as default
  # Leave it empty if you don't need comments
  # Choose: Disqus/Disqusjs/Livere/Gitalk/Valine/Waline/Utterances/Facebook Comments/Twikoo/Giscus/Remark42/Artalk
  # Format of two comments system : Disqus,Waline
  use:
  # Display the comment name next to the button
  text: true
  # Lazyload: The comment system will be load when comment element enters the browser's viewport.
  # If you set it to true, the comment count will be invalid
  lazyload: false
  # Display comment count in post's top_img
  count: false
  # Display comment count in Home Page
  card_post_count: false

# Disqus
# https://disqus.com/
disqus:
  shortname:
  # For newest comments widget
  apikey:

# Alternative Disqus - Render comments with Disqus API
# https://github.com/SukkaW/DisqusJS
disqusjs:
  shortname:
  apikey:
  option:

# Livere
# https://www.livere.com/
livere:
  uid:

# Gitalk
# https://github.com/gitalk/gitalk
gitalk:
  client_id:
  client_secret:
  repo:
  owner:
  admin:
  option:

# Valine
# https://valine.js.org
valine:
  appId:
  appKey:
  avatar: monsterid
  # This configuration is suitable for domestic custom domain name users, overseas version will be automatically detected (no need to manually fill in)
  serverURLs:
  bg:
  # Use Valine visitor count as the page view count
  visitor: false
  option:

# Waline - A simple comment system with backend support fork from Valine
# https://waline.js.org/
waline:
  serverURL:
  bg:
  # Use Waline pageview count as the page view count
  pageview: false
  option:

# Utterances
# https://utteranc.es/
utterances:
  repo:
  # Issue Mapping: pathname/url/title/og:title
  issue_term: pathname
  # Theme: github-light/github-dark/github-dark-orange/icy-dark/dark-blue/photon-dark
  light_theme: github-light
  dark_theme: photon-dark
  js:
  option:

# Facebook Comments Plugin
# https://developers.facebook.com/docs/plugins/comments/
facebook_comments:
  app_id:
  # optional
  user_id:
  pageSize: 10
  # Choose: social / time / reverse_time
  order_by: social
  lang: en_US

# Twikoo
# https://github.com/imaegoo/twikoo
twikoo:
  envId:
  region:
  # Use Twikoo visitor count as the page view count
  visitor: false
  option:

# Giscus
# https://giscus.app/
giscus:
  repo:
  repo_id:
  category_id:
  light_theme: light
  dark_theme: dark
  js:
  option:

# Remark42
# https://remark42.com/docs/configuration/frontend/
remark42:
  host:
  siteId:
  option:

# Artalk
# https://artalk.js.org/guide/frontend/config.html
artalk:
  server:
  site:
  # Use Artalk visitor count as the page view count
  visitor: false
  option:

# --------------------------------------
# Chat Services
# --------------------------------------

chat:
  # Choose: chatra/tidio/crisp
  # Leave it empty if you don't need chat
  use:
  # Chat Button [recommend]
  # It will create a button in the bottom right corner of website, and hide the origin button
  rightside_button: false
  # The origin chat button is displayed when scrolling up, and the button is hidden when scrolling down
  button_hide_show: false

# https://chatra.io/
chatra:
  id:

# https://www.tidio.com/
tidio:
  public_key:

# https://crisp.chat/en/
crisp:
  website_id:

# --------------------------------------
# Analysis
# --------------------------------------

# https://tongji.baidu.com/web/welcome/login
baidu_analytics: 

# https://analytics.google.com/analytics/web/
google_analytics: G-Z11Q7GYJG7

# https://www.cloudflare.com/zh-tw/web-analytics/
cloudflare_analytics: 8082fa6433824052ad84d98b4b119a04

# https://clarity.microsoft.com/
microsoft_clarity:

# https://umami.is/
umami_analytics:
  enable: false
  # For self-hosted setups, configure the hostname of the Umami instance
  serverURL:
  website_id:
  option:
  UV_PV:
    site_uv: false
    site_pv: false
    page_pv: false
    # Umami Cloud (API key) / self-hosted Umami (token)
    token:

# --------------------------------------
# Advertisement
# --------------------------------------

# Google Adsense
google_adsense:
  enable: false
  auto_ads: true
  js: https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js
  client:
  enable_page_level_ads: true

# Insert ads manually
# Leave it empty if you don't need ads
ad:
  # Insert ads in the index (every three posts)
  index:
  # Insert ads in aside
  aside:
  # Insert ads in the post (before pagination)
  post:

# --------------------------------------
# Verification
# --------------------------------------

site_verification:
  - name: google-site-verification
    content: 8HpNQzFS-Pk3GGv1V9m-SKiNnxAGFWafZlsuopT3X3M
  - name: baidu-site-verification
    content: codeva-kP8T9p7Lz9

# --------------------------------------
# Beautify / Effect
# --------------------------------------

# Theme color for customize
# Notice: color value must in double quotes like "#000" or may cause error!

# theme_color:
#   enable: true
#   main: "#49B1F5"
#   paginator: "#00c4b6"
#   button_hover: "#FF7242"
#   text_selection: "#00c4b6"
#   link_color: "#99a9bf"
#   meta_color: "#858585"
#   hr_color: "#A4D8FA"
#   code_foreground: "#F47466"
#   code_background: "rgba(27, 31, 35, .05)"
#   toc_color: "#00c4b6"
#   blockquote_padding_color: "#49b1f5"
#   blockquote_background_color: "#49b1f5"
#   scrollbar_color: "#49b1f5"
#   meta_theme_color_light: "ffffff"
#   meta_theme_color_dark: "#0d0d0d"

# The user interface setting of category and tag page
# Choose: index - same as Homepage UI / default - same as archives UI
# leave it empty or index
category_ui:
tag_ui:

# Rounded corners for UI elements
rounded_corners_ui: true

# Stretches the lines so that each line has equal width
text_align_justify: false

# Add a mask to the header and footer
mask:
  header: true
  footer: true

# Loading Animation
preloader:
  enable: false
  # source
  # 1. fullpage-loading
  # 2. pace (progress bar)
  source: 1
  # pace theme (see https://codebyzach.github.io/pace/)
  pace_css_url: 

# Page Transition
enter_transitions: true

# Default display mode - light (default) / dark
display_mode: light

# Configuration for beautifying the content of the article
beautify:
  enable: true
  # Specify the field to beautify (site or post)
  field: post
  # Specify the icon to be used as a prefix for the title, such as '\f0c1'
  title_prefix_icon: '\f061'
  # Specify the color of the title prefix icon, such as '#F47466'
  title_prefix_icon_color: '#74C0FC'
# Global font settings
# Don't modify the following settings unless you know how they work
font:
  global_font_size: 
  code_font_size:
  font_family: "'LXGW WenKai', sans-serif"
  code_font_family:

# Font settings for the site title and site subtitle
blog_title_font:
  font_link:
  font_family: "'LXGW WenKai', sans-serif"

# The setting of divider icon
hr_icon:
  enable: true
  # The unicode value of Font Awesome icon, such as '\3423'
  icon:
  icon_top:

# Typewriter Effect
# https://github.com/disjukr/activate-power-mode
activate_power_mode:
  enable: false
  colorful: true
  shake: true
  mobile: false

# Background effects
# --------------------------------------

# canvas_ribbon
# See: https://github.com/hustcc/ribbon.js
canvas_ribbon:
  enable: false
  # The size of ribbon
  size: 150
  # The opacity of ribbon (0 ~ 1)
  alpha: 0.6
  zIndex: -1
  click_to_change: false
  mobile: false

# Fluttering Ribbon
# 是否开启綵带特效
canvas_fluttering_ribbon:
  enable: false
  mobile: false

# canvas_nest
# https://github.com/hustcc/canvas-nest.js
canvas_nest:
  enable: true
  # Color of lines, default: '0,0,0'; RGB values: (R,G,B).(note: use ',' to separate.)
  color: '0,0,255'
  # The opacity of line (0~1)
  opacity: 0.7
  # The z-index property of the background
  zIndex: -1
  # The number of lines
  count: 99
  mobile: false

# Mouse click effects: fireworks
fireworks:
  enable: false
  zIndex: 9999
  mobile: false

# Mouse click effects: Heart symbol
click_heart:
  enable: false
  mobile: false

# Mouse click effects: words
clickShowText:
  enable: false
  text:
    # - I
    # - LOVE
    # - YOU
  fontSize: 15px
  random: false
  mobile: false

# --------------------------------------
# Lightbox Settings
# --------------------------------------

# Choose: fancybox / medium_zoom
# https://github.com/francoischalifour/medium-zoom
# https://fancyapps.com/fancybox/
# Leave it empty if you don't need lightbox
lightbox:

# --------------------------------------
# Tag Plugins settings
# --------------------------------------

# Series
series:
  enable: false
  # Order by title or date
  orderBy: 'title'
  # Sort of order. 1, asc for ascending; -1, desc for descending
  order: 1
  number: true

# ABCJS - The ABC Music Notation Plugin
# https://github.com/paulrosen/abcjs
abcjs:
  enable: false
  per_page: true

# Mermaid
# https://github.com/mermaid-js/mermaid
mermaid:
  enable: false
  # Write Mermaid diagrams using code blocks
  code_write: false
  # built-in themes: default / forest / dark / neutral
  theme:
    light: default
    dark: dark

# chartjs
# see https://www.chartjs.org/docs/latest/
chartjs:
  enable: false
  # Do not modify unless you understand how they work.
  # The default settings are only used when the MD syntax is not specified.
  # General font color for the chart
  fontColor:
    light: 'rgba(0, 0, 0, 0.8)'
    dark: 'rgba(255, 255, 255, 0.8)'
  # General border color for the chart
  borderColor:
    light: 'rgba(0, 0, 0, 0.1)'
    dark: 'rgba(255, 255, 255, 0.2)'
  # Background color for scale labels on radar and polar area charts
  scale_ticks_backdropColor:
    light: 'transparent'
    dark: 'transparent'

# Note - Bootstrap Callout
note:
  # Note tag style values:
  #  - simple    bs-callout old alert style. Default.
  #  - modern    bs-callout new (v2-v3) alert style.
  #  - flat      flat callout style with background, like on Mozilla or StackOverflow.
  #  - disabled  disable all CSS styles import of note tag.
  style: flat
  icons: true
  border_radius: 3
  # Offset lighter of background in % for modern and flat styles (modern: -12 | 12; flat: -18 | 6).
  # Offset also applied to label tag variables. This option can work with disabled note tag.
  light_bg_offset: 0

# --------------------------------------
# Other Settings
# --------------------------------------

# https://github.com/MoOx/pjax
pjax:
  enable: false
  # Exclude the specified pages from pjax, such as '/music/'
  exclude:
    # - /xxxxxx/

# Inject
# Insert the code to head (before '</head>' tag) and the bottom (before '</body>' tag)
inject:
  head:
    - <link rel="stylesheet" href="/css/custom.css">
    - <link rel="stylesheet" href="/css/font.css">
  bottom:
    # - <script src="xxxx"></script>
    # 樱花飘落效果
    - <script src="/js/sakura-control.js"></script>
    - <script async src="https://npm.elemecdn.com/tzy-blog/lib/js/other/sakura.js"></script>
    # 鼠标样式
    - <link rel="stylesheet" href="/css/cursors.css">
    # 自定义图标替换脚本
    - <script src="/js/custom-icons.js"></script>
    # 日历卡片
    - <script src="/js/calendar.js"></script>
    - <link rel="stylesheet" href="/css/calendar.css">
    - <script src="https://open.lightxi.com/unpkg/chinese-lunar@0.1.4/lib/chinese-lunar.js"></script>

# Inject the css and script (aplayer/meting)
aplayerInject:
  enable: false
  per_page: true
# Snackbar - Toast Notification
# https://github.com/polonel/SnackBar
# position: top-left / top-center / top-right / bottom-left / bottom-center / bottom-right
snackbar:
  enable: false
  position: bottom-left
  # The background color of Toast Notification in light mode and dark mode
  bg_light: '#49b1f5'
  bg_dark: '#1f1f1f'

# Instant.page
# https://instant.page/
instantpage: false

# Lazyload
# https://github.com/verlok/vanilla-lazyload
lazyload:
  enable: false
  # Specify the field to use lazyload (site or post)
  field: site
  placeholder:
  blur: false

# PWA
# See https://github.com/JLHwung/hexo-offline
# ---------------
pwa:
  enable: false
  manifest:
  apple_touch_icon:
  favicon_32_32:
  favicon_16_16:
  mask_icon:

# Open graph meta tags
# https://hexo.io/docs/helpers#open-graph
Open_Graph_meta:
  enable: true
  option:
    # twitter_card:
    # twitter_image:
    # twitter_id:
    # twitter_site:
    # google_plus:
    # fb_admins:
    # fb_app_id:

# Structured Data
# https://developers.google.com/search/docs/guides/intro-structured-data
structured_data: true

# Add the vendor prefixes to ensure compatibility
css_prefix: true

# CDN Settings
# Don't modify the following settings unless you know how they work
CDN:
  # The CDN provider for internal and third-party scripts
  # Options for both: local/jsdelivr/unpkg/cdnjs/custom
  # Note: Dev version can only use 'local' for internal scripts
  # Note: When setting third-party scripts to 'local', you need to install hexo-butterfly-extjs
  internal_provider: local
  third_party_provider: jsdelivr

  # Add version number to url, true or false
  version: false






  # Custom format
  # For example: https://cdn.staticfile.org/${cdnjs_name}/${version}/${min_cdnjs_file}
  custom_format:

  option:
    # abcjs_basic_js:
    # activate_power_mode:
    # algolia_js:
    # algolia_search:
    # aplayer_css:
    # aplayer_js:
    # artalk_css:
    # artalk_js:
    # blueimp_md5:
    # busuanzi:
    # canvas_fluttering_ribbon:
    # canvas_nest:
    # canvas_ribbon:
    # chartjs:
    # click_heart:
    # clickShowText:
    # disqusjs:
    # disqusjs_css:
    # docsearch_css:
    # docsearch_js:
    # egjs_infinitegrid:
    # fancybox:
    # fancybox_css:
    # fireworks:
    # fontawesome:
    # gitalk:
    # gitalk_css:
    # giscus:
    # instantpage:
    # instantsearch:
    # katex:
    # katex_copytex:
    # lazyload:
    # local_search:
    # main:
    # main_css:
    # mathjax:
    # medium_zoom:
    # mermaid:
    # meting_js:
    # prismjs_autoloader:
    # prismjs_js:
    # prismjs_lineNumber_js:
    # pjax:
    # sharejs:
    # sharejs_css:
    # snackbar:
    # snackbar_css:
    # translate:
    # twikoo:
    # typed:
    # utils:
    # valine:
    # waline_css:
    # waline_js:

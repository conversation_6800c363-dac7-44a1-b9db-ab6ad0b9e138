/**
 * 樱花动画控制脚本
 * 用于管理樱花飘落动画的启动、停止和状态控制
 */

// 等待樱花脚本加载完成
document.addEventListener('DOMContentLoaded', function() {
  // 检查localStorage中的樱花动画状态
  const sakuraEnabled = localStorage.getItem('sakura-animation-enabled') !== 'false';
  
  // 如果樱花动画被禁用，阻止自动启动
  if (!sakuraEnabled) {
    // 重写startSakura函数，阻止自动启动
    if (typeof window.startSakura === 'function') {
      const originalStartSakura = window.startSakura;
      window.startSakura = function() {
        // 只有在启用状态下才启动
        if (localStorage.getItem('sakura-animation-enabled') !== 'false') {
          originalStartSakura();
        }
      };
    }
    
    // 如果img.onload已经触发，需要停止动画
    setTimeout(() => {
      if (typeof window.staticx !== 'undefined' && window.staticx && typeof window.stopp === 'function') {
        window.stopp();
      }
    }, 1000);
  }
});

// 监听樱花脚本的加载
(function() {
  let checkCount = 0;
  const maxChecks = 50; // 最多检查50次，避免无限循环
  
  function checkSakuraScript() {
    checkCount++;
    
    if (typeof window.stopp === 'function' && typeof window.staticx !== 'undefined') {
      // 樱花脚本已加载，检查状态
      const sakuraEnabled = localStorage.getItem('sakura-animation-enabled') !== 'false';
      
      if (!sakuraEnabled && window.staticx) {
        // 如果禁用且正在运行，则停止
        window.stopp();
      }
      
      return; // 脚本已加载，停止检查
    }
    
    if (checkCount < maxChecks) {
      setTimeout(checkSakuraScript, 200);
    }
  }
  
  // 开始检查
  setTimeout(checkSakuraScript, 500);
})();

// 提供全局控制函数
window.sakuraControl = {
  // 启动樱花动画
  start: function() {
    if (typeof window.startSakura === 'function') {
      if (typeof window.staticx === 'undefined' || !window.staticx) {
        window.startSakura();
      }
    } else if (typeof window.stopp === 'function' && typeof window.staticx !== 'undefined' && !window.staticx) {
      window.stopp();
    }
  },
  
  // 停止樱花动画
  stop: function() {
    if (typeof window.stopp === 'function' && typeof window.staticx !== 'undefined' && window.staticx) {
      window.stopp();
    }
  },
  
  // 切换樱花动画状态
  toggle: function() {
    if (typeof window.staticx !== 'undefined') {
      if (window.staticx) {
        this.stop();
      } else {
        this.start();
      }
    }
  },
  
  // 获取当前状态
  isRunning: function() {
    return typeof window.staticx !== 'undefined' && window.staticx;
  }
};
